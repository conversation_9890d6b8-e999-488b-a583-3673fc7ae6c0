/* CSS Variables for Modern Theme Colors */
:root {
    /* Primary Brand Colors */
    --primary-blue: #1e40af;
    --secondary-blue: #3b82f6;
    --accent-blue: #60a5fa;
    --light-blue: #dbeafe;
    --dark-blue: #1e3a8a;

    /* Neutral Colors */
    --primary-gray: #64748b;
    --light-gray: #f1f5f9;
    --medium-gray: #e2e8f0;
    --dark-gray: #334155;
    --white: #ffffff;
    --black: #0f172a;

    /* Status Colors */
    --success: #059669;
    --success-light: #d1fae5;
    --warning: #d97706;
    --warning-light: #fef3c7;
    --danger: #dc2626;
    --danger-light: #fee2e2;
    --info: #0891b2;
    --info-light: #cffafe;

    /* Background Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --bg-accent: #f0f9ff;

    /* Text Colors */
    --text-primary: #0f172a;
    --text-secondary: #475569;
    --text-muted: #94a3b8;

    /* Border and Shadow */
    --border-color: #e2e8f0;
    --border-light: #f1f5f9;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
    --gradient-accent: linear-gradient(135deg, var(--accent-blue), var(--secondary-blue));
    --gradient-success: linear-gradient(135deg, var(--success), #10b981);
    --gradient-warning: linear-gradient(135deg, var(--warning), #f59e0b);
    --gradient-danger: linear-gradient(135deg, var(--danger), #ef4444);

    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius: 0.5rem;
    --radius-md: 0.75rem;
    --radius-lg: 1rem;
    --radius-xl: 1.5rem;

    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

/* Dark Theme Colors */
[data-theme="dark"] {
    /* Background Colors */
    --bg-primary: #1e293b;
    --bg-secondary: #0f172a;
    --bg-tertiary: #334155;
    --bg-accent: #1e3a8a;

    /* Text Colors */
    --text-primary: #f1f5f9;
    --text-secondary: #cbd5e1;
    --text-muted: #64748b;

    /* Border and Shadow */
    --border-color: #475569;
    --border-light: #334155;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.4);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.5);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.6);

    /* Status Colors - Darker variants */
    --success-light: #064e3b;
    --warning-light: #78350f;
    --danger-light: #7f1d1d;
    --info-light: #164e63;

    /* Neutral adjustments */
    --light-gray: #334155;
    --medium-gray: #475569;
    --dark-gray: #cbd5e1;
}

/* Global Styles */
*,
*::before,
*::after {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: var(--bg-secondary);
    color: var(--text-primary);
    line-height: 1.7;
    font-weight: 400;
    transition: all var(--transition);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    overflow-x: hidden;
}

.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    position: relative;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-tertiary);
    border-radius: var(--radius);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-gray);
    border-radius: var(--radius);
    transition: background var(--transition);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--dark-gray);
}

/* Selection Styling */
::selection {
    background: var(--light-blue);
    color: var(--primary-blue);
}

::-moz-selection {
    background: var(--light-blue);
    color: var(--primary-blue);
}

/* Loading Animation */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0,0,0);
    }
    40%, 43% {
        transform: translate3d(0, -30px, 0);
    }
    70% {
        transform: translate3d(0, -15px, 0);
    }
    90% {
        transform: translate3d(0, -4px, 0);
    }
}

@keyframes slideInFromRight {
    0% {
        transform: translateX(100%);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInFromLeft {
    0% {
        transform: translateX(-100%);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInFromTop {
    0% {
        transform: translateY(-100%);
        opacity: 0;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes slideInFromBottom {
    0% {
        transform: translateY(100%);
        opacity: 0;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Utility Classes */
.loading {
    animation: spin 1s linear infinite;
}

.bounce {
    animation: bounce 2s infinite;
}

.slide-in-right {
    animation: slideInFromRight 0.5s ease-out;
}

.slide-in-left {
    animation: slideInFromLeft 0.5s ease-out;
}

.slide-in-top {
    animation: slideInFromTop 0.5s ease-out;
}

.slide-in-bottom {
    animation: slideInFromBottom 0.5s ease-out;
}

/* Focus Styles */
*:focus {
    outline: 2px solid var(--primary-blue);
    outline-offset: 2px;
}

button:focus,
.btn:focus {
    outline: 3px solid rgba(37, 99, 235, 0.5);
    outline-offset: 2px;
}

/* Smooth Transitions for Theme Changes */
* {
    transition: background-color var(--transition),
                color var(--transition),
                border-color var(--transition),
                box-shadow var(--transition);
}

/* Header Styles */
.header {
    background: var(--gradient-primary);
    color: white;
    padding: 1.25rem 0;
    box-shadow: var(--shadow-lg);
    position: sticky;
    top: 0;
    z-index: 1000;
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 2rem;
}

.logo {
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all var(--transition);
}

.logo:hover {
    transform: translateX(5px);
}

.logo i {
    font-size: 2.5rem;
    color: rgba(255, 255, 255, 0.9);
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.logo-image {
    width: 55px;
    height: 55px;
    object-fit: contain;
    border-radius: var(--radius-md);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    transition: all var(--transition);
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.logo-image:hover {
    transform: scale(1.08) rotate(2deg);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 255, 255, 0.4);
}

/* Fallback when logo image fails to load */
.logo-image:not([src]),
.logo-image[src=""] {
    display: none;
}

.logo-image:not([src]) + i,
.logo-image[src=""] + i {
    display: inline-block;
}

.logo h1 {
    font-size: 1.75rem;
    font-weight: 800;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    letter-spacing: -0.025em;
}

.header-controls {
    display: flex;
    align-items: center;
    gap: 1.25rem;
}

.theme-toggle {
    background: rgba(255, 255, 255, 0.15);
    border: 2px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 0.75rem;
    border-radius: 50%;
    cursor: pointer;
    transition: all var(--transition);
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.theme-toggle::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transition: all var(--transition);
    transform: translate(-50%, -50%);
}

.theme-toggle:hover::before {
    width: 100%;
    height: 100%;
}

.theme-toggle:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.4);
    transform: scale(1.1) rotate(15deg);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.theme-toggle i {
    font-size: 1.2rem;
    z-index: 1;
    position: relative;
}

.user-menu {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1.25rem;
    background: rgba(255, 255, 255, 0.15);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-xl);
    position: relative;
    cursor: pointer;
    transition: all var(--transition);
    backdrop-filter: blur(10px);
    font-weight: 600;
}

.user-menu:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.4);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.user-menu i {
    font-size: 1.4rem;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.user-dropdown {
    position: absolute;
    top: calc(100% + 0.75rem);
    right: 0;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    min-width: 220px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-15px) scale(0.95);
    transition: all var(--transition);
    z-index: 1000;
    backdrop-filter: blur(20px);
    overflow: hidden;
}

.user-dropdown::before {
    content: '';
    position: absolute;
    top: -8px;
    right: 20px;
    width: 16px;
    height: 16px;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-bottom: none;
    border-right: none;
    transform: rotate(45deg);
}

.user-menu.active .user-dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    width: 100%;
    padding: 1rem 1.25rem;
    border: none;
    background: none;
    color: var(--text-primary);
    text-align: right;
    cursor: pointer;
    transition: all var(--transition);
    font-size: 0.95rem;
    font-weight: 500;
    position: relative;
}

.dropdown-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 0;
    height: 100%;
    background: var(--gradient-primary);
    transition: width var(--transition);
}

.dropdown-item:hover {
    background: var(--bg-secondary);
    color: var(--primary-blue);
    transform: translateX(-5px);
}

.dropdown-item:hover::before {
    width: 4px;
}

.dropdown-item i {
    font-size: 1.1rem;
    opacity: 0.8;
    transition: all var(--transition);
}

.dropdown-item:hover i {
    opacity: 1;
    transform: scale(1.1);
}

.dropdown-item:first-child {
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

.dropdown-item:last-child {
    border-radius: 0 0 var(--radius-lg) var(--radius-lg);
}

/* Navigation Styles */
.main-nav {
    display: flex;
    gap: 0.75rem;
    align-items: center;
    flex-wrap: wrap;
}

.nav-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid transparent;
    color: white;
    padding: 0.875rem 1.75rem;
    border-radius: var(--radius-xl);
    cursor: pointer;
    transition: all var(--transition);
    display: flex;
    align-items: center;
    gap: 0.625rem;
    font-size: 0.95rem;
    font-weight: 600;
    text-transform: none;
    letter-spacing: 0.025em;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.nav-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--transition-slow);
}

.nav-btn:hover::before {
    left: 100%;
}

.nav-btn:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.nav-btn.active {
    background: rgba(255, 255, 255, 0.35);
    border-color: rgba(255, 255, 255, 0.5);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
    transform: translateY(-2px);
}

.nav-btn.active::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    transform: translateX(-50%);
    width: 60%;
    height: 3px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: var(--radius);
}

.nav-btn i {
    font-size: 1.1rem;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

/* Content Sections */
.content-section {
    display: none;
    animation: fadeIn 0.5s ease-in-out;
}

.content-section.active {
    display: block;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Main Content */
.main-content {
    flex: 1;
    max-width: 1400px;
    margin: 0 auto;
    padding: 2.5rem;
    width: 100%;
    position: relative;
}

/* Section Styles */
.event-input-section,
.events-log-section,
.stats-overview,
.charts-section,
.risk-analysis,
.event-types-management,
.user-management {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    padding: 2.5rem;
    margin-bottom: 2.5rem;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
    position: relative;
    overflow: hidden;
    transition: all var(--transition);
}

.event-input-section::before,
.events-log-section::before,
.stats-overview::before,
.charts-section::before,
.risk-analysis::before,
.event-types-management::before,
.user-management::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    border-radius: var(--radius-xl) var(--radius-xl) 0 0;
}

.event-input-section:hover,
.events-log-section:hover,
.stats-overview:hover,
.charts-section:hover,
.risk-analysis:hover,
.event-types-management:hover,
.user-management:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2.5rem;
    padding-bottom: 1.5rem;
    border-bottom: 2px solid var(--light-blue);
    position: relative;
}

.section-header::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 60px;
    height: 4px;
    background: var(--gradient-primary);
    border-radius: var(--radius);
}

.section-header h2 {
    color: var(--primary-blue);
    font-size: 1.75rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    letter-spacing: -0.025em;
}

.section-header h2 i {
    font-size: 1.5rem;
    opacity: 0.9;
}

/* Form Styles */
.event-form,
.user-form {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    position: relative;
}

.form-group label {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.95rem;
    margin-bottom: 0.25rem;
    transition: all var(--transition);
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 1rem 1.25rem;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-lg);
    font-size: 1rem;
    background: var(--bg-primary);
    color: var(--text-primary);
    transition: all var(--transition);
    font-family: inherit;
    position: relative;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 4px rgba(37, 99, 235, 0.1);
    transform: translateY(-2px);
}

.form-group input:focus + label,
.form-group select:focus + label,
.form-group textarea:focus + label {
    color: var(--primary-blue);
    transform: translateY(-2px);
}

/* Floating label effect */
.form-group.floating {
    position: relative;
}

.form-group.floating input,
.form-group.floating textarea {
    padding-top: 1.5rem;
    padding-bottom: 0.5rem;
}

.form-group.floating label {
    position: absolute;
    top: 1rem;
    left: 1.25rem;
    background: var(--bg-primary);
    padding: 0 0.5rem;
    transition: all var(--transition);
    pointer-events: none;
    z-index: 1;
}

.form-group.floating input:focus + label,
.form-group.floating input:not(:placeholder-shown) + label,
.form-group.floating textarea:focus + label,
.form-group.floating textarea:not(:placeholder-shown) + label {
    top: -0.5rem;
    font-size: 0.85rem;
    color: var(--primary-blue);
    font-weight: 700;
}

/* Special input types */
.form-group input[type="number"] {
    text-align: right;
}

.form-group input[readonly] {
    background-color: var(--bg-secondary);
    color: var(--text-secondary);
    cursor: not-allowed;
    opacity: 0.8;
}

.form-help {
    display: block;
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin-top: 0.25rem;
    font-style: italic;
    line-height: 1.4;
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

/* Button Styles */
.btn {
    padding: 0.875rem 1.75rem;
    border: 2px solid transparent;
    border-radius: var(--radius-lg);
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition);
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    text-decoration: none;
    position: relative;
    overflow: hidden;
    text-transform: none;
    letter-spacing: 0.025em;
    min-height: 48px;
    justify-content: center;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--transition-slow);
}

.btn:hover::before {
    left: 100%;
}

.btn i {
    font-size: 1.1rem;
    transition: all var(--transition);
}

.btn:hover i {
    transform: scale(1.1);
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow);
}

.btn-primary:hover {
    background: var(--gradient-accent);
    transform: translateY(-3px) scale(1.02);
    box-shadow: var(--shadow-lg);
    border-color: rgba(255, 255, 255, 0.3);
}

.btn-secondary {
    background: var(--primary-gray);
    color: white;
    box-shadow: var(--shadow);
}

.btn-secondary:hover {
    background: var(--dark-gray);
    transform: translateY(-3px) scale(1.02);
    box-shadow: var(--shadow-lg);
}

.btn-danger {
    background: var(--gradient-danger);
    color: white;
    box-shadow: var(--shadow);
}

.btn-danger:hover {
    background: linear-gradient(135deg, #dc2626, #b91c1c);
    transform: translateY(-3px) scale(1.02);
    box-shadow: var(--shadow-lg);
}

.btn-success {
    background: var(--gradient-success);
    color: white;
    box-shadow: var(--shadow);
}

.btn-success:hover {
    background: linear-gradient(135deg, #059669, #047857);
    transform: translateY(-3px) scale(1.02);
    box-shadow: var(--shadow-lg);
}

.btn:active {
    transform: translateY(-1px) scale(0.98);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
}

.btn:disabled::before {
    display: none;
}

.form-actions {
    display: flex;
    gap: 1.25rem;
    justify-content: flex-start;
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 2px solid var(--border-light);
    position: relative;
}

.form-actions::before {
    content: '';
    position: absolute;
    top: -2px;
    left: 0;
    width: 80px;
    height: 4px;
    background: var(--gradient-primary);
    border-radius: var(--radius);
}

/* Log Controls */
.log-controls {
    display: flex;
    gap: 1.5rem;
    align-items: center;
    flex-wrap: wrap;
    margin-bottom: 1.5rem;
    padding: 1.5rem;
    background: var(--bg-accent);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-light);
}

.search-input,
.filter-select {
    padding: 0.875rem 1.25rem;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-lg);
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 1rem;
    transition: all var(--transition);
    font-family: inherit;
    position: relative;
}

.search-input {
    min-width: 300px;
    padding-left: 3rem;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%236b7280'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z'%3E%3C/path%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: 1rem center;
    background-size: 1.25rem;
}

.search-input:focus,
.filter-select:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 4px rgba(37, 99, 235, 0.1);
    transform: translateY(-2px);
}

.filter-select {
    min-width: 180px;
    cursor: pointer;
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%236b7280'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: calc(100% - 1rem) center;
    background-size: 1rem;
    padding-right: 3rem;
}

/* Table Styles */
.events-table-container {
    overflow-x: auto;
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-lg);
    background: var(--bg-primary);
    position: relative;
    max-height: 70vh;
    overflow-y: auto;
}

/* Custom scrollbar for table container */
.events-table-container::-webkit-scrollbar {
    width: 12px;
    height: 12px;
}

.events-table-container::-webkit-scrollbar-track {
    background: var(--bg-tertiary);
    border-radius: var(--radius);
}

.events-table-container::-webkit-scrollbar-thumb {
    background: var(--primary-blue);
    border-radius: var(--radius);
    border: 2px solid var(--bg-tertiary);
}

.events-table-container::-webkit-scrollbar-thumb:hover {
    background: var(--dark-blue);
}

.events-table-container::-webkit-scrollbar-corner {
    background: var(--bg-tertiary);
}

.events-table-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    border-radius: var(--radius-xl) var(--radius-xl) 0 0;
}

.events-table {
    width: 100%;
    border-collapse: collapse;
    background: var(--bg-primary);
    min-width: 1400px;
    border-radius: var(--radius-xl);
    overflow: hidden;
    font-size: 1rem;
    table-layout: fixed;
}

/* Table column widths - Optimized for better readability */
.events-table th:nth-child(1), /* Serial Number */
.events-table td:nth-child(1) {
    width: 140px;
    min-width: 140px;
}

.events-table th:nth-child(2), /* Title */
.events-table td:nth-child(2) {
    width: 280px;
    min-width: 280px;
}

.events-table th:nth-child(3), /* Type */
.events-table td:nth-child(3) {
    width: 160px;
    min-width: 160px;
}

.events-table th:nth-child(4), /* Severity */
.events-table td:nth-child(4) {
    width: 120px;
    min-width: 120px;
}

.events-table th:nth-child(5), /* Responsible Person */
.events-table td:nth-child(5) {
    width: 200px;
    min-width: 200px;
}

.events-table th:nth-child(6), /* Costs */
.events-table td:nth-child(6) {
    width: 220px;
    min-width: 220px;
}

.events-table th:nth-child(7), /* Date */
.events-table td:nth-child(7) {
    width: 160px;
    min-width: 160px;
}

.events-table th:nth-child(8), /* Status */
.events-table td:nth-child(8) {
    width: 120px;
    min-width: 120px;
}

.events-table th:nth-child(9), /* Actions */
.events-table td:nth-child(9) {
    width: 140px;
    min-width: 140px;
}

.events-table th,
.events-table td {
    padding: 1.5rem 1.25rem;
    text-align: right;
    border-bottom: 1px solid var(--border-light);
    transition: all var(--transition);
    vertical-align: middle;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

.events-table th {
    background: var(--gradient-primary);
    color: white;
    font-weight: 700;
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    position: sticky;
    top: 0;
    z-index: 10;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    height: 60px;
}

.events-table th:first-child {
    border-radius: var(--radius-lg) 0 0 0;
}

.events-table th:last-child {
    border-radius: 0 var(--radius-lg) 0 0;
}

.events-table tbody tr {
    transition: all var(--transition);
    position: relative;
}

.events-table tbody tr:hover {
    background: var(--bg-accent);
    transform: scale(1.01);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.events-table tbody tr:hover td {
    border-color: var(--primary-blue);
}

.events-table tbody tr:nth-child(even) {
    background: var(--bg-tertiary);
}

.events-table tbody tr:nth-child(even):hover {
    background: var(--bg-accent);
}

.events-table td {
    font-size: 1rem;
    line-height: 1.6;
    min-height: 70px;
    height: auto;
}

/* Specific styling for different columns */
.events-table td:nth-child(1) { /* Serial Number */
    font-weight: 600;
    font-family: 'Courier New', monospace;
    color: var(--primary-blue);
}

.events-table td:nth-child(2) { /* Title */
    font-weight: 600;
    color: var(--text-primary);
    max-width: 280px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.events-table td:nth-child(2):hover {
    white-space: normal;
    overflow: visible;
    text-overflow: unset;
}

.events-table td:nth-child(3) { /* Type */
    font-weight: 500;
    color: var(--text-secondary);
}

.events-table td:nth-child(5) { /* Responsible Person */
    font-weight: 500;
    color: var(--text-primary);
}

.events-table td:nth-child(7) { /* Date */
    font-family: 'Cairo', sans-serif;
    font-size: 0.9rem;
    color: var(--text-secondary);
    direction: rtl;
    text-align: right;
    font-weight: 500;
}

/* Number formatting */
.events-table .number-cell {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    text-align: center;
    background: var(--bg-accent);
    border-radius: var(--radius-sm);
    padding: 0.25rem 0.5rem;
    display: inline-block;
    min-width: 60px;
}

/* Date formatting */
.events-table .date-cell {
    font-family: 'Cairo', sans-serif;
    font-size: 0.9rem;
    color: var(--text-secondary);
    direction: rtl;
    text-align: right;
    white-space: nowrap;
    font-weight: 500;
}

.events-table .date-cell .date-part {
    display: block;
    line-height: 1.4;
    margin-bottom: 0.25rem;
}

.events-table .date-cell .date-part.date {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.95rem;
}

.events-table .date-cell .date-part.time {
    font-size: 0.85rem;
    opacity: 0.8;
    color: var(--text-secondary);
}

/* Gregorian date styling */
.gregorian-date {
    font-family: 'Cairo', sans-serif;
    direction: rtl;
    text-align: right;
    color: var(--text-primary);
    font-weight: 500;
}

.gregorian-date .date-separator {
    color: var(--text-secondary);
    margin: 0 0.25rem;
}

/* Post Incident Review Styles */
.post-incident-review {
    padding: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.review-form-container {
    background: var(--card-bg);
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border: 1px solid var(--border-color);
}

.post-incident-form {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.form-section {
    background: var(--bg-color);
    border-radius: 8px;
    padding: 1.5rem;
    border: 1px solid var(--border-color);
}

.form-section h3 {
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    font-size: 1.2rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 0.5rem;
}

.form-section h3 i {
    font-size: 1.1rem;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.95rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 0.75rem;
    border: 2px solid var(--border-color);
    border-radius: 6px;
    font-size: 0.95rem;
    font-family: 'Cairo', sans-serif;
    background: var(--card-bg);
    color: var(--text-primary);
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
    line-height: 1.6;
}

.form-group textarea::placeholder {
    color: var(--text-secondary);
    font-style: italic;
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    padding-top: 1.5rem;
    border-top: 1px solid var(--border-color);
}

.form-actions .btn {
    padding: 0.75rem 2rem;
    font-size: 1rem;
    font-weight: 600;
    border-radius: 6px;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.form-actions .btn-primary {
    background: var(--primary-color);
    color: white;
    border: none;
}

.form-actions .btn-primary:hover {
    background: var(--primary-hover);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

/* Custom styling for save review button */
#saveReviewBtn {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border: none;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    transition: all 0.3s ease;
}

#saveReviewBtn:hover {
    background: linear-gradient(135deg, #218838, #1ea085);
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

#saveReviewBtn:active {
    transform: translateY(-1px);
    box-shadow: 0 3px 10px rgba(40, 167, 69, 0.3);
}

#saveReviewBtn i {
    margin-left: 0.5rem;
    font-size: 1.1rem;
}

/* Animation for save button */
#saveReviewBtn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

#saveReviewBtn:hover::before {
    left: 100%;
}

/* Reset button styling */
#resetReviewBtn {
    background: linear-gradient(135deg, #6c757d, #5a6268);
    color: white;
    border: none;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
    transition: all 0.3s ease;
}

#resetReviewBtn:hover {
    background: linear-gradient(135deg, #5a6268, #495057);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
}

#resetReviewBtn:active {
    transform: translateY(-1px);
    box-shadow: 0 3px 10px rgba(108, 117, 125, 0.3);
}

/* Button loading state */
.btn-loading {
    position: relative;
    pointer-events: none;
    opacity: 0.7;
}

.btn-loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    margin: auto;
    border: 2px solid transparent;
    border-top-color: #ffffff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Custom styling for save event button */
#saveEventBtn {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border: none;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

#saveEventBtn:hover {
    background: linear-gradient(135deg, #0056b3, #004085);
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
}

#saveEventBtn:active {
    transform: translateY(-1px);
    box-shadow: 0 3px 10px rgba(0, 123, 255, 0.3);
}

#saveEventBtn i {
    margin-left: 0.5rem;
    font-size: 1.1rem;
}

/* Animation for save event button */
#saveEventBtn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

#saveEventBtn:hover::before {
    left: 100%;
}

/* Reset event button styling */
#resetEventBtn {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
    border: none;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
    transition: all 0.3s ease;
}

#resetEventBtn:hover {
    background: linear-gradient(135deg, #c82333, #bd2130);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
}

#resetEventBtn:active {
    transform: translateY(-1px);
    box-shadow: 0 3px 10px rgba(220, 53, 69, 0.3);
}

.form-actions .btn-secondary {
    background: var(--text-secondary);
    color: white;
    border: none;
}

.form-actions .btn-secondary:hover {
    background: #5a6c7d;
    transform: translateY(-2px);
}

/* Reviews List Section */
.reviews-list-section {
    background: var(--card-bg);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border: 1px solid var(--border-color);
}

.reviews-table-container {
    overflow-x: auto;
    margin-top: 1rem;
}

.reviews-table {
    width: 100%;
    border-collapse: collapse;
    background: var(--card-bg);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.reviews-table th {
    background: var(--primary-color);
    color: white;
    padding: 1rem;
    text-align: right;
    font-weight: 600;
    font-size: 0.95rem;
}

.reviews-table td {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    color: var(--text-primary);
    font-size: 0.9rem;
}

.reviews-table tbody tr:hover {
    background: var(--hover-bg);
}

.reviews-table tbody tr:last-child td {
    border-bottom: none;
}

/* Status badges for reviews */
.preparedness-badge,
.response-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-align: center;
    display: inline-block;
    min-width: 80px;
}

.preparedness-badge.prepared,
.response-badge.complete {
    background: #d4edda;
    color: #155724;
}

.preparedness-badge.partial,
.response-badge.partial {
    background: #fff3cd;
    color: #856404;
}

.preparedness-badge.unprepared,
.response-badge.poor {
    background: #f8d7da;
    color: #721c24;
}

/* Responsive design for post incident review */
@media (max-width: 768px) {
    .post-incident-review {
        padding: 1rem;
    }

    .review-form-container,
    .reviews-list-section {
        padding: 1rem;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .form-actions {
        flex-direction: column;
    }

    .form-actions .btn {
        width: 100%;
        justify-content: center;
    }
}

/* Review Details Modal */
.review-details-modal {
    max-width: 1000px;
    max-height: 90vh;
    overflow-y: auto;
}

.review-details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 1.5rem;
    margin-bottom: 1rem;
}

.detail-section {
    background: var(--bg-color);
    border-radius: 8px;
    padding: 1.5rem;
    border: 1px solid var(--border-color);
}

.detail-section.full-width {
    grid-column: 1 / -1;
}

.detail-section h4 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-size: 1.1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 0.5rem;
}

.detail-item {
    margin-bottom: 0.75rem;
    line-height: 1.6;
}

.detail-item strong {
    color: var(--text-primary);
    font-weight: 600;
    margin-left: 0.5rem;
}

.detail-text {
    background: var(--card-bg);
    padding: 1rem;
    border-radius: 6px;
    border: 1px solid var(--border-color);
    line-height: 1.6;
    color: var(--text-primary);
    margin-bottom: 1rem;
    white-space: pre-wrap;
}

/* Button styles for reviews table */
.btn-sm {
    padding: 0.4rem 0.8rem;
    font-size: 0.85rem;
    margin: 0 0.2rem;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-info {
    background: #17a2b8;
    color: white;
}

.btn-info:hover {
    background: #138496;
    transform: translateY(-1px);
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
    transform: translateY(-1px);
}

/* Responsive design for review details modal */
@media (max-width: 768px) {
    .review-details-modal {
        max-width: 95vw;
        margin: 1rem;
    }

    .review-details-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .detail-section {
        padding: 1rem;
    }
}

.events-table tbody tr:last-child td:first-child {
    border-radius: 0 0 0 var(--radius-lg);
}

.events-table tbody tr:last-child td:last-child {
    border-radius: 0 0 var(--radius-lg) 0;
}

/* Tooltip for long text */
.text-tooltip {
    position: relative;
    cursor: help;
}

.text-tooltip:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--black);
    color: white;
    padding: 0.75rem 1rem;
    border-radius: var(--radius);
    font-size: 0.85rem;
    white-space: nowrap;
    z-index: 1000;
    box-shadow: var(--shadow-lg);
    max-width: 300px;
    white-space: normal;
    word-wrap: break-word;
}

.text-tooltip:hover::before {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%) translateY(100%);
    border: 6px solid transparent;
    border-top-color: var(--black);
    z-index: 1000;
}

/* Row highlighting on selection */
.events-table tbody tr.selected {
    background: var(--light-blue) !important;
    border-left: 4px solid var(--primary-blue);
}

.events-table tbody tr.selected td {
    color: var(--primary-blue);
    font-weight: 600;
}

/* Empty state styling */
.events-table tbody.empty::after {
    content: 'لا توجد أحداث أمنية مسجلة';
    display: block;
    text-align: center;
    padding: 3rem;
    color: var(--text-secondary);
    font-style: italic;
    font-size: 1.1rem;
    background: var(--bg-tertiary);
    border-radius: var(--radius);
    margin: 1rem;
}

/* Severity Badges */
.severity-badge {
    padding: 0.5rem 1rem;
    border-radius: var(--radius-xl);
    font-size: 0.85rem;
    font-weight: 700;
    text-align: center;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all var(--transition);
    box-shadow: var(--shadow-sm);
    border: 2px solid transparent;
}

.severity-badge::before {
    content: '';
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: currentColor;
    opacity: 0.8;
}

.severity-badge:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: var(--shadow);
}

.severity-low {
    background: var(--success-light);
    color: var(--success);
    border-color: var(--success);
}

.severity-medium {
    background: var(--warning-light);
    color: var(--warning);
    border-color: var(--warning);
}

.severity-high {
    background: var(--danger-light);
    color: var(--danger);
    border-color: var(--danger);
}

.severity-critical {
    background: linear-gradient(135deg, #fecaca, #fee2e2);
    color: #991b1b;
    border-color: #991b1b;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% {
        box-shadow: 0 0 0 0 rgba(153, 27, 27, 0.4);
    }
    50% {
        box-shadow: 0 0 0 8px rgba(153, 27, 27, 0);
    }
}

/* Dark theme severity badges */
[data-theme="dark"] .severity-low {
    background: #166534;
    color: #dcfce7;
}

[data-theme="dark"] .severity-medium {
    background: #92400e;
    color: #fef3c7;
}

[data-theme="dark"] .severity-high {
    background: #c53030;
    color: #fed7d7;
}

[data-theme="dark"] .severity-critical {
    background: #991b1b;
    color: #fecaca;
}

/* Status Badges */
.status-badge {
    padding: 0.5rem 1rem;
    border-radius: var(--radius-xl);
    font-size: 0.85rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all var(--transition);
    box-shadow: var(--shadow-sm);
    border: 2px solid transparent;
}

.status-badge::before {
    content: '';
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: currentColor;
    opacity: 0.8;
}

.status-badge:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: var(--shadow);
}

.status-open {
    background: var(--info-light);
    color: var(--info);
    border-color: var(--info);
}

.status-investigating {
    background: var(--warning-light);
    color: var(--warning);
    border-color: var(--warning);
}

.status-resolved {
    background: var(--success-light);
    color: var(--success);
    border-color: var(--success);
}

.status-closed {
    background: var(--medium-gray);
    color: var(--dark-gray);
    border-color: var(--dark-gray);
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 0.75rem;
    align-items: center;
    justify-content: center;
}

.action-btn {
    padding: 0.5rem;
    border: 2px solid transparent;
    border-radius: var(--radius);
    cursor: pointer;
    font-size: 1rem;
    transition: all var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    position: relative;
    overflow: hidden;
}

.action-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transition: all var(--transition);
    transform: translate(-50%, -50%);
}

.action-btn:hover::before {
    width: 100%;
    height: 100%;
}

.action-btn:hover {
    transform: translateY(-3px) scale(1.1);
    box-shadow: var(--shadow-lg);
}

.action-btn:active {
    transform: translateY(-1px) scale(1.05);
}

.action-btn i {
    z-index: 1;
    position: relative;
}

/* Costs display in table */
.cost-item {
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
    padding: 0.5rem 0.75rem;
    border-radius: var(--radius);
    background: var(--bg-secondary);
    border: 1px solid var(--border-light);
    transition: all var(--transition);
    display: block;
    line-height: 1.4;
}

.cost-item:hover {
    background: var(--bg-accent);
    transform: translateX(-2px);
}

.cost-item.direct {
    border-left: 4px solid var(--primary-blue);
    background: linear-gradient(90deg, var(--light-blue), var(--bg-secondary));
}

.cost-item.indirect {
    border-left: 4px solid var(--warning);
    background: linear-gradient(90deg, var(--warning-light), var(--bg-secondary));
}

.cost-item strong {
    color: var(--text-primary);
    font-weight: 600;
    display: block;
    margin-bottom: 0.25rem;
}

.cost-item .cost-value {
    font-family: 'Courier New', monospace;
    font-weight: 700;
    color: var(--primary-blue);
}

.no-costs {
    color: var(--text-secondary);
    font-style: italic;
    font-size: 0.95rem;
    padding: 1rem;
    text-align: center;
    background: var(--bg-tertiary);
    border-radius: var(--radius);
    border: 1px dashed var(--border-color);
}

/* Alert styles */
.alert-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.alert {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 16px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    min-width: 300px;
    animation: slideIn 0.3s ease-out;
}

.alert-error {
    background: #fee;
    color: #c53030;
    border-left: 4px solid #e53e3e;
}

.alert-success {
    background: #f0fff4;
    color: #2f855a;
    border-left: 4px solid #38a169;
}

.alert-info {
    background: #ebf8ff;
    color: #2b6cb0;
    border-left: 4px solid #3182ce;
}

.alert-close {
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    margin-left: auto;
}

.alert-close:hover {
    background: rgba(0, 0, 0, 0.1);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Event Types Management */
.event-types-management {
    padding: 2rem;
}

.event-types-container {
    margin-top: 2rem;
}

.event-types-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

.event-type-card {
    background: var(--bg-primary);
    border: 2px solid var(--border-color);
    border-radius: 12px;
    padding: 1.5rem;
    transition: all 0.3s ease;
    position: relative;
}

.event-type-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: var(--primary-blue);
}

.event-type-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.event-type-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.event-type-info h3 {
    color: var(--text-primary);
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0 0 0.25rem 0;
}

.event-type-key {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-family: 'Courier New', monospace;
    background: var(--bg-secondary);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    display: inline-block;
}

.event-type-description {
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.5;
    margin: 1rem 0;
}

.event-type-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

.event-type-actions .action-btn {
    padding: 0.5rem;
    border-radius: 6px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.edit-type-btn {
    background: var(--primary-blue);
    color: white;
}

.edit-type-btn:hover {
    background: var(--secondary-blue);
}

.delete-type-btn {
    background: var(--danger-color);
    color: white;
}

.delete-type-btn:hover {
    background: #dc2626;
}

.event-type-usage {
    position: absolute;
    top: 1rem;
    left: 1rem;
    background: var(--bg-secondary);
    color: var(--text-secondary);
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.8rem;
}

.empty-event-types {
    text-align: center;
    padding: 3rem;
    color: var(--text-secondary);
}

.empty-event-types i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* Form help text */
.form-help {
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin-top: 0.25rem;
    display: block;
}

/* Disabled button styles */
.action-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.action-btn:disabled:hover {
    transform: none;
    background: var(--danger-color);
}

.view-btn {
    background: var(--info);
    color: white;
    border-color: var(--info);
}

.view-btn:hover {
    background: #0891b2;
    border-color: #0891b2;
}

.edit-btn {
    background: var(--warning);
    color: white;
    border-color: var(--warning);
}

.edit-btn:hover {
    background: #d97706;
    border-color: #d97706;
}

.delete-btn {
    background: var(--danger);
    color: white;
    border-color: var(--danger);
}

.delete-btn:hover {
    background: #dc2626;
    border-color: #dc2626;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .main-content {
        max-width: 100%;
        padding: 2rem;
    }

    .header-content {
        max-width: 100%;
    }
}

@media (max-width: 992px) {
    .main-nav {
        gap: 0.5rem;
    }

    .nav-btn {
        padding: 0.75rem 1.25rem;
        font-size: 0.9rem;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .events-table {
        min-width: 1100px;
        font-size: 0.95rem;
    }

    .events-table th,
    .events-table td {
        padding: 1.25rem 1rem;
    }
}

@media (max-width: 768px) {
    .header {
        padding: 1rem 0;
    }

    .header-content {
        padding: 0 1rem;
        flex-wrap: wrap;
        gap: 1rem;
    }

    .logo h1 {
        font-size: 1.25rem;
    }

    .logo-image {
        width: 45px;
        height: 45px;
    }

    .main-nav {
        order: 3;
        width: 100%;
        justify-content: center;
        flex-wrap: wrap;
    }

    .nav-btn {
        flex: 1;
        min-width: 100px;
        max-width: 140px;
        padding: 0.75rem 1rem;
        font-size: 0.85rem;
    }

    .header-controls {
        gap: 1rem;
    }

    .theme-toggle {
        width: 44px;
        height: 44px;
    }

    .main-content {
        padding: 1.5rem;
    }

    .event-input-section,
    .events-log-section,
    .stats-overview,
    .charts-section,
    .risk-analysis,
    .event-types-management,
    .user-management {
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
        margin-bottom: 1.5rem;
    }

    .section-header h2 {
        font-size: 1.5rem;
    }

    .log-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .search-input {
        min-width: auto;
    }

    .events-table {
        font-size: 0.9rem;
        min-width: 900px;
    }

    .events-table th,
    .events-table td {
        padding: 1rem 0.75rem;
    }

    .events-table th {
        font-size: 0.9rem;
        height: 55px;
    }

    .events-table td {
        min-height: 60px;
    }

    .form-actions {
        flex-direction: column;
        gap: 1rem;
    }

    .btn {
        justify-content: center;
        min-height: 52px;
    }

    .modal-content {
        width: 98%;
        max-height: 90vh;
    }

    .modal-header {
        padding: 1.5rem;
    }

    .modal-body {
        padding: 1.5rem;
    }
}

@media (max-width: 480px) {
    .header-content {
        flex-direction: column;
        text-align: center;
    }

    .main-nav {
        order: 2;
        margin-top: 0;
    }

    .header-controls {
        order: 3;
        margin-top: 1rem;
    }

    .nav-btn {
        min-width: 80px;
        padding: 0.625rem 0.75rem;
        font-size: 0.8rem;
    }

    .nav-btn span {
        display: none;
    }

    .main-content {
        padding: 1rem;
    }

    .event-input-section,
    .events-log-section,
    .stats-overview,
    .charts-section,
    .risk-analysis,
    .event-types-management,
    .user-management {
        padding: 1rem;
    }

    .section-header h2 {
        font-size: 1.25rem;
    }

    .events-table {
        min-width: 700px;
        font-size: 0.85rem;
    }

    .events-table th,
    .events-table td {
        padding: 0.75rem 0.5rem;
    }

    .events-table th {
        font-size: 0.8rem;
        height: 50px;
    }

    .events-table td {
        min-height: 55px;
    }

    /* Adjust column widths for mobile */
    .events-table th:nth-child(2),
    .events-table td:nth-child(2) {
        width: 200px;
        min-width: 200px;
    }

    .events-table th:nth-child(6),
    .events-table td:nth-child(6) {
        width: 180px;
        min-width: 180px;
    }

    .action-buttons {
        flex-direction: column;
        gap: 0.5rem;
    }

    .action-btn {
        width: 100%;
        height: 40px;
        border-radius: var(--radius-lg);
    }
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    z-index: 10000;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
    animation: modalFadeIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        backdrop-filter: blur(0px);
    }
    to {
        opacity: 1;
        backdrop-filter: blur(10px);
    }
}

.modal-content {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    max-width: 700px;
    width: 95%;
    max-height: 85vh;
    overflow: hidden;
    box-shadow: var(--shadow-xl);
    border: 1px solid var(--border-color);
    animation: modalSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    border-radius: var(--radius-xl) var(--radius-xl) 0 0;
}

.modal-header {
    padding: 2rem 2.5rem 1.5rem;
    border-bottom: 2px solid var(--border-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--bg-accent);
    position: relative;
}

.modal-header::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 2.5rem;
    width: 60px;
    height: 4px;
    background: var(--gradient-primary);
    border-radius: var(--radius);
}

.modal-header h3 {
    color: var(--primary-blue);
    font-weight: 700;
    font-size: 1.5rem;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.modal-header h3 i {
    font-size: 1.25rem;
    opacity: 0.9;
}

.modal-close {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid transparent;
    font-size: 1.25rem;
    cursor: pointer;
    color: var(--text-secondary);
    padding: 0.75rem;
    border-radius: 50%;
    transition: all var(--transition);
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.modal-close::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: var(--danger-light);
    border-radius: 50%;
    transition: all var(--transition);
    transform: translate(-50%, -50%);
}

.modal-close:hover::before {
    width: 100%;
    height: 100%;
}

.modal-close:hover {
    background: var(--danger-light);
    border-color: var(--danger);
    color: var(--danger);
    transform: scale(1.1) rotate(90deg);
}

.modal-close i {
    z-index: 1;
    position: relative;
}

.modal-body {
    padding: 2.5rem;
    max-height: calc(85vh - 120px);
    overflow-y: auto;
}
}

.modal-footer {
    padding: 1.5rem;
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

/* Event Details Styles */
.event-detail {
    margin-bottom: 1rem;
}

.event-detail label {
    font-weight: 600;
    color: var(--text-secondary);
    display: block;
    margin-bottom: 0.25rem;
    font-size: 0.9rem;
}

.event-detail .value {
    color: var(--text-primary);
    font-size: 1rem;
    padding: 0.5rem 0;
}

.event-detail .detailed-description {
    background: var(--bg-secondary);
    padding: 1rem;
    border-radius: 8px;
    border-left: 4px solid var(--primary-blue);
    line-height: 1.6;
    white-space: pre-wrap;
    max-height: 200px;
    overflow-y: auto;
}



/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Loading Spinner */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 3rem;
    color: var(--text-secondary);
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: var(--primary-gray);
}

.empty-state h3 {
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 1rem 1.5rem;
    border-radius: 8px;
    color: white;
    font-weight: 600;
    z-index: 1100;
    animation: slideInRight 0.3s ease;
    max-width: 400px;
}

.notification.success {
    background: var(--success);
}

.notification.error {
    background: var(--danger);
}

.notification.warning {
    background: var(--warning);
}

.notification.info {
    background: var(--info);
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Statistics Styles */
.stats-overview {
    margin-bottom: 2rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 1.5rem;
}

.stat-card {
    background: var(--bg-primary);
    border: 2px solid var(--border-color);
    border-radius: 12px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--primary-blue);
}

.stat-card.critical::before {
    background: var(--danger);
}

.stat-card.high::before {
    background: var(--warning);
}

.stat-card.open::before {
    background: var(--info);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px var(--shadow);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--light-blue);
    color: var(--primary-blue);
    font-size: 1.5rem;
}

.stat-card.critical .stat-icon {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger);
}

.stat-card.high .stat-icon {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning);
}

.stat-card.open .stat-icon {
    background: rgba(6, 182, 212, 0.1);
    color: var(--info);
}

.stat-content h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

.stat-content p {
    color: var(--text-secondary);
    margin: 0.25rem 0 0 0;
    font-size: 0.9rem;
}

/* Charts Styles */
.charts-section {
    background: var(--bg-primary);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 20px var(--shadow);
    border: 1px solid var(--border-color);
}

.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-top: 1.5rem;
}

.chart-container {
    background: var(--bg-secondary);
    border-radius: 8px;
    padding: 1.5rem;
    border: 1px solid var(--border-color);
}

.chart-header {
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--light-blue);
}

.chart-header h3 {
    color: var(--primary-blue);
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.chart-content {
    position: relative;
    height: 300px;
}

.chart-content canvas {
    max-width: 100%;
    max-height: 100%;
}

/* Risk Analysis Styles */
.risk-analysis {
    background: var(--bg-primary);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 20px var(--shadow);
    border: 1px solid var(--border-color);
}

.risk-score-container {
    margin-bottom: 2rem;
}

.risk-score-card {
    background: var(--bg-secondary);
    border-radius: 12px;
    padding: 2rem;
    border: 1px solid var(--border-color);
}

.risk-score-header {
    text-align: center;
    margin-bottom: 1.5rem;
}

.risk-score-header h3 {
    color: var(--primary-blue);
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
}

.risk-score-content {
    display: flex;
    align-items: center;
    gap: 2rem;
    justify-content: center;
}

.risk-gauge {
    width: 200px;
    height: 200px;
}

.risk-details {
    text-align: center;
}

.risk-level {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--warning);
}

.risk-score {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-blue);
    margin-bottom: 1rem;
}

.risk-description {
    color: var(--text-secondary);
    font-size: 1rem;
}

/* Risk Categories */
.risk-categories {
    margin-bottom: 2rem;
}

.risk-category {
    background: var(--bg-secondary);
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.risk-category:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px var(--shadow);
}

.risk-category-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.risk-category-header h4 {
    color: var(--text-primary);
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.risk-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.risk-badge.low {
    background: #dcfce7;
    color: #166534;
}

.risk-badge.medium {
    background: #fef3c7;
    color: #92400e;
}

.risk-badge.high {
    background: #fed7d7;
    color: #c53030;
}

.risk-badge.critical {
    background: #fecaca;
    color: #991b1b;
}

[data-theme="dark"] .risk-badge.low {
    background: #166534;
    color: #dcfce7;
}

[data-theme="dark"] .risk-badge.medium {
    background: #92400e;
    color: #fef3c7;
}

[data-theme="dark"] .risk-badge.high {
    background: #c53030;
    color: #fed7d7;
}

[data-theme="dark"] .risk-badge.critical {
    background: #991b1b;
    color: #fecaca;
}

.risk-category-content p {
    color: var(--text-secondary);
    margin: 0.5rem 0 0 0;
    font-size: 0.9rem;
}

.risk-progress {
    width: 100%;
    height: 8px;
    background: var(--bg-tertiary);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.risk-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--success), var(--warning), var(--danger));
    border-radius: 4px;
    transition: width 0.3s ease;
}

/* Recommendations */
.recommendations {
    background: var(--bg-secondary);
    border-radius: 8px;
    padding: 1.5rem;
    border: 1px solid var(--border-color);
}

.recommendations-list {
    margin-top: 1rem;
}

.recommendation-item {
    background: var(--bg-primary);
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    border-left: 4px solid var(--primary-blue);
    transition: all 0.3s ease;
}

.recommendation-item:hover {
    transform: translateX(5px);
    box-shadow: 0 2px 10px var(--shadow);
}

.recommendation-item.high-priority {
    border-left-color: var(--danger);
}

.recommendation-item.medium-priority {
    border-left-color: var(--warning);
}

.recommendation-item.low-priority {
    border-left-color: var(--success);
}

.recommendation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.recommendation-title {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 1rem;
}

.recommendation-priority {
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
}

.recommendation-priority.high {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger);
}

.recommendation-priority.medium {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning);
}

.recommendation-priority.low {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success);
}

.recommendation-description {
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.5;
}

/* Modern Login Page Styles */
.login-body {
    margin: 0;
    padding: 0;
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    font-family: 'Cairo', sans-serif;
}

[data-theme="dark"] .login-body {
    background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
}

/* Animated Background */
.animated-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1;
}

.floating-shapes {
    position: relative;
    width: 100%;
    height: 100%;
}

.shape {
    position: absolute;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    animation: float 20s infinite linear;
}

.shape-1 {
    width: 80px;
    height: 80px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 120px;
    height: 120px;
    top: 60%;
    left: 80%;
    animation-delay: 5s;
}

.shape-3 {
    width: 60px;
    height: 60px;
    top: 80%;
    left: 20%;
    animation-delay: 10s;
}

.shape-4 {
    width: 100px;
    height: 100px;
    top: 30%;
    left: 70%;
    animation-delay: 15s;
}

.shape-5 {
    width: 40px;
    height: 40px;
    top: 10%;
    left: 60%;
    animation-delay: 8s;
}

.shape-6 {
    width: 90px;
    height: 90px;
    top: 70%;
    left: 50%;
    animation-delay: 12s;
}

@keyframes float {
    0% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.7;
    }
    50% {
        transform: translateY(-100px) rotate(180deg);
        opacity: 0.3;
    }
    100% {
        transform: translateY(0px) rotate(360deg);
        opacity: 0.7;
    }
}

/* Modern Login Container */
.login-container {
    display: flex;
    min-height: 100vh;
    width: 100%;
    position: relative;
    z-index: 2;
    max-width: 1400px;
    margin: 0 auto;
    box-shadow: 0 0 50px rgba(0, 0, 0, 0.3);
    border-radius: 20px;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

/* Left Side - Branding */
.login-branding {
    flex: 1;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    position: relative;
}

.brand-content {
    text-align: center;
    color: white;
}

.brand-logo {
    margin-bottom: 3rem;
}

.logo-circle {
    width: 120px;
    height: 120px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.logo-circle i {
    font-size: 3rem;
    color: white;
}

.logo-circle .logo-image {
    width: 80px;
    height: 80px;
    object-fit: contain;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.logo-circle .logo-image:hover {
    transform: scale(1.1);
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
}

.brand-content h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(45deg, #fff, #e2e8f0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.brand-tagline {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 3rem;
    line-height: 1.6;
}

.features-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    text-align: right;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.feature-item:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateX(-5px);
}

.feature-item i {
    font-size: 1.5rem;
    color: #fbbf24;
    min-width: 30px;
}

.feature-item span {
    font-size: 1.1rem;
    font-weight: 500;
}

/* Right Side - Login Form */
.login-form-container {
    flex: 1;
    background: var(--bg-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.login-card {
    width: 100%;
    max-width: 450px;
    padding: 3rem;
}

[data-theme="dark"] .login-form-container {
    background: var(--bg-primary);
}

/* Modern Login Header */
.login-header {
    text-align: center;
    margin-bottom: 3rem;
}

.login-header h2 {
    color: var(--text-primary);
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.login-subtitle {
    color: var(--text-secondary);
    font-size: 1rem;
    margin: 0;
    opacity: 0.8;
}

/* Modern Form Styles */
.modern-login-form {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.form-group {
    position: relative;
}

.input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.input-wrapper input {
    width: 100%;
    padding: 1.2rem 3rem 1.2rem 1rem;
    border: 2px solid var(--border-color);
    border-radius: 12px;
    font-size: 1rem;
    background: var(--bg-secondary);
    color: var(--text-primary);
    transition: all 0.3s ease;
    direction: rtl;
    text-align: right;
}

.input-wrapper input:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    background: var(--bg-primary);
}

.input-wrapper input:focus + label,
.input-wrapper input:not(:placeholder-shown) + label {
    top: -8px;
    right: 12px;
    font-size: 0.8rem;
    color: var(--primary-blue);
    background: var(--bg-primary);
    padding: 0 8px;
}

.input-wrapper label {
    position: absolute;
    top: 50%;
    right: 3rem;
    transform: translateY(-50%);
    color: var(--text-secondary);
    font-size: 1rem;
    transition: all 0.3s ease;
    pointer-events: none;
    z-index: 1;
}

.input-icon {
    position: absolute;
    right: 1rem;
    color: var(--text-secondary);
    font-size: 1.1rem;
    z-index: 2;
    transition: all 0.3s ease;
}

.input-wrapper input:focus ~ .input-icon {
    color: var(--primary-blue);
}

.login-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.input-group {
    position: relative;
    display: flex;
    align-items: center;
    direction: rtl;
}

.input-group i {
    position: absolute;
    right: 1rem;
    color: var(--text-secondary);
    z-index: 2;
    pointer-events: none;
}

.input-group input {
    width: 100%;
    padding: 1rem 3rem 1rem 1rem;
    border: 2px solid var(--border-color);
    border-radius: 12px;
    font-size: 1rem;
    background: var(--bg-primary);
    color: var(--text-primary);
    transition: all 0.3s ease;
    direction: rtl;
    text-align: right;
}

.input-group input:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.input-group input::placeholder {
    color: var(--text-secondary);
    opacity: 0.7;
    text-align: right;
}

.input-group input:focus + i,
.input-group input:not(:placeholder-shown) + i {
    color: var(--primary-blue);
}

/* Fix for Safari and other browsers */
.input-group input:-webkit-autofill {
    -webkit-box-shadow: 0 0 0 30px var(--bg-primary) inset !important;
    -webkit-text-fill-color: var(--text-primary) !important;
}

.input-group input:-webkit-autofill:focus {
    -webkit-box-shadow: 0 0 0 30px var(--bg-primary) inset !important;
    -webkit-text-fill-color: var(--text-primary) !important;
}

/* Ensure proper text direction */
.input-group input[type="text"],
.input-group input[type="password"] {
    unicode-bidi: plaintext;
}

/* Password Toggle */
.password-toggle {
    position: absolute;
    left: 1rem;
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    z-index: 3;
    top: 50%;
    transform: translateY(-50%);
}

.password-toggle:hover {
    color: var(--primary-blue);
    background: rgba(37, 99, 235, 0.1);
}

/* Modern Form Options */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 1rem 0;
}

.modern-checkbox {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    user-select: none;
}

.modern-checkbox input[type="checkbox"] {
    display: none;
}

.modern-checkbox .checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    background: var(--bg-secondary);
}

.modern-checkbox input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-blue);
    border-color: var(--primary-blue);
}

.modern-checkbox input[type="checkbox"]:checked + .checkmark i {
    color: white;
    font-size: 0.8rem;
}

.checkbox-text {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.forgot-password {
    color: var(--primary-blue);
    text-decoration: none;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.forgot-password:hover {
    text-decoration: underline;
    opacity: 0.8;
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.checkbox-container {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.checkbox-container input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid var(--border-color);
    border-radius: 4px;
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-container input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-blue);
    border-color: var(--primary-blue);
}

.checkbox-container input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

/* Modern Login Button */
.modern-login-btn {
    background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
    color: white;
    border: none;
    padding: 1.2rem 2rem;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    position: relative;
    overflow: hidden;
    width: 100%;
    margin: 1rem 0;
}

.modern-login-btn:hover:not(:disabled) {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(37, 99, 235, 0.4);
}

.modern-login-btn:active {
    transform: translateY(-1px);
}

.modern-login-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.btn-text {
    flex: 1;
    text-align: center;
}

.btn-arrow {
    transition: all 0.3s ease;
}

.modern-login-btn:hover .btn-arrow {
    transform: translateX(-5px);
}

.btn-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.login-error {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid var(--danger);
    color: var(--danger);
    padding: 1rem;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.login-footer {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
}

/* Divider */
.divider {
    text-align: center;
    margin: 2rem 0;
    position: relative;
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--border-color);
}

.divider span {
    background: var(--bg-primary);
    padding: 0 1rem;
    color: var(--text-secondary);
    font-size: 0.9rem;
}



/* Login Background Animation */
.login-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    overflow: hidden;
}

.security-icons {
    position: absolute;
    width: 100%;
    height: 100%;
}

.security-icons i {
    position: absolute;
    color: rgba(255, 255, 255, 0.1);
    font-size: 2rem;
    animation: float 6s ease-in-out infinite;
}

.security-icons i:nth-child(1) {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.security-icons i:nth-child(2) {
    top: 60%;
    left: 20%;
    animation-delay: 1s;
}

.security-icons i:nth-child(3) {
    top: 30%;
    right: 15%;
    animation-delay: 2s;
}

.security-icons i:nth-child(4) {
    bottom: 30%;
    right: 25%;
    animation-delay: 3s;
}

.security-icons i:nth-child(5) {
    bottom: 20%;
    left: 30%;
    animation-delay: 4s;
}

.security-icons i:nth-child(6) {
    top: 50%;
    right: 40%;
    animation-delay: 5s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.1;
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
        opacity: 0.3;
    }
}

/* Login Theme Toggle */
.login-theme-toggle {
    position: fixed;
    top: 2rem;
    left: 2rem;
    z-index: 1000;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    padding: 0.75rem;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.login-theme-toggle:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* User Management Styles */
.user-management {
    background: var(--bg-primary);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 20px var(--shadow);
    border: 1px solid var(--border-color);
}

.users-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-top: 1.5rem;
}

.user-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 1.5rem;
    transition: all 0.3s ease;
    position: relative;
}

.user-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px var(--shadow);
}

.user-card.inactive {
    opacity: 0.6;
    border-color: var(--primary-gray);
}

.user-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.user-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--primary-blue);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    font-weight: 600;
}

.user-info h4 {
    color: var(--text-primary);
    margin: 0 0 0.25rem 0;
    font-size: 1.1rem;
}

.user-info p {
    color: var(--text-secondary);
    margin: 0;
    font-size: 0.9rem;
}

.user-role {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.role-admin {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger);
}

.role-analyst {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning);
}

.role-operator {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success);
}

.user-permissions {
    margin-bottom: 1rem;
}

.user-permissions h5 {
    color: var(--text-primary);
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.permission-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
}

.permission-tag {
    background: var(--light-blue);
    color: var(--primary-blue);
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 500;
}

.user-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
}

.user-action-btn {
    padding: 0.5rem;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.3s ease;
}

.edit-user-btn {
    background: var(--info);
    color: white;
}

.delete-user-btn {
    background: var(--danger);
    color: white;
}

.toggle-user-btn {
    background: var(--warning);
    color: white;
}

.user-action-btn:hover {
    transform: scale(1.1);
    opacity: 0.8;
}

/* User Form Styles */
.user-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.permissions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 0.5rem;
}

.permission-checkbox {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.permission-checkbox:hover {
    background: var(--bg-secondary);
}

.permission-checkbox input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: var(--primary-blue);
}

.permission-checkbox span {
    font-size: 0.9rem;
    color: var(--text-primary);
}

/* Activity Log Styles */
.activity-log {
    background: var(--bg-primary);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 20px var(--shadow);
    border: 1px solid var(--border-color);
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.activity-item:hover {
    background: var(--bg-secondary);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    flex-shrink: 0;
}

.activity-login {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success);
}

.activity-logout {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger);
}

.activity-user_management {
    background: rgba(37, 99, 235, 0.1);
    color: var(--primary-blue);
}

.activity-event {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning);
}

.activity-content {
    flex: 1;
}

.activity-description {
    color: var(--text-primary);
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.activity-meta {
    color: var(--text-secondary);
    font-size: 0.8rem;
    display: flex;
    gap: 1rem;
}

/* Responsive Updates */
@media (max-width: 768px) {
    .user-dropdown {
        right: -50px;
        min-width: 180px;
    }

    .permissions-grid {
        grid-template-columns: 1fr;
    }

    .users-grid {
        grid-template-columns: 1fr;
    }

    .activity-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .activity-meta {
        flex-direction: column;
        gap: 0.25rem;
    }

    /* Modern Login Mobile Styles */
    .login-container {
        flex-direction: column;
        border-radius: 0;
        min-height: 100vh;
    }

    .login-branding {
        flex: none;
        padding: 2rem 1rem;
        min-height: 40vh;
    }

    .brand-content h1 {
        font-size: 2rem;
    }

    .brand-tagline {
        font-size: 1rem;
        margin-bottom: 2rem;
    }

    .logo-circle .logo-image {
        width: 60px;
        height: 60px;
    }

    .features-list {
        display: none;
    }

    .login-form-container {
        flex: 1;
        padding: 1rem;
    }

    .login-card {
        padding: 2rem 1rem;
    }

    .login-header h2 {
        font-size: 1.5rem;
    }

    .input-wrapper input {
        padding: 1rem 2.5rem 1rem 0.75rem;
        font-size: 0.9rem;
    }

    .input-icon {
        right: 0.75rem;
        font-size: 1rem;
    }

    .password-toggle {
        left: 0.75rem;
        padding: 0.25rem;
    }



    .form-options {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
}

@media (max-width: 480px) {
    .header-content {
        flex-direction: column;
        gap: 1rem;
    }

    .main-nav {
        order: -1;
        width: 100%;
        justify-content: center;
    }

    .nav-btn {
        flex: 1;
        justify-content: center;
        padding: 0.5rem;
        font-size: 0.8rem;
    }

    .nav-btn span {
        display: none;
    }

    .section-header {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .action-buttons {
        flex-direction: column;
    }

    .modal-content {
        width: 95%;
        margin: 1rem;
    }

    .modal-footer {
        flex-direction: column;
    }

    .notification {
        right: 10px;
        left: 10px;
        max-width: none;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .charts-grid {
        grid-template-columns: 1fr;
    }

    .risk-score-content {
        flex-direction: column;
        gap: 1rem;
    }
}
