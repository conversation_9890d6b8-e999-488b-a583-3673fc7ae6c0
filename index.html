<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>نظام تسجيل أحداث أمن المعلومات</title>
  <style>
    :root {
      --primary: #1a237e;
      --secondary: #3949ab;
      --danger: #d32f2f;
      --gray: #ececec;
      --dark: #222;
      --light: #fff;
      --accent: #1976d2;
    // ...existing code...
    body {
      font-family: 'Cairo', Arial, sans-serif;
      background: var(--gray);
      color: var(--dark);
      margin: 0;
      padding: 0;
      transition: background 0.3s, color 0.3s;
    }
    .dark-mode {
      background: #181c24;
      color: #e3e3e3;
    }
    .container {
      max-width: 420px;
      margin: 60px auto;
      background: var(--light);
      border-radius: 10px;
      box-shadow: 0 2px 12px #0002;
      padding: 32px 24px 24px 24px;
    }
    .dark-mode .container {
      background: #232a36;
      box-shadow: 0 2px 12px #0006;
    }
    h2 {
      text-align: center;
      margin-bottom: 24px;
      color: var(--primary);
    }
    .form-group {
      margin-bottom: 18px;
    }
    label {
      display: block;
      margin-bottom: 6px;
      font-weight: bold;
    }
    input[type="text"],
    input[type="password"],
    input[type="datetime-local"],
    select,
    textarea {
      width: 100%;
      padding: 8px 10px;
      border: 1px solid #bbb;
      border-radius: 5px;
      font-size: 1em;
      background: var(--gray);
      color: var(--dark);
      box-sizing: border-box;
      transition: border 0.2s;
    }
    .dark-mode input,
    .dark-mode select,
    .dark-mode textarea {
      background: #232a36;
      color: #e3e3e3;
      border: 1px solid #444;
    }
    button {
      width: 100%;
      padding: 10px;
      background: var(--primary);
      color: var(--light);
      border: none;
      border-radius: 5px;
      font-size: 1.1em;
      cursor: pointer;
      margin-top: 8px;
      transition: background 0.2s;
    }
    button:hover {
      background: var(--secondary);
    }
    .toggle-mode {
      position: fixed;
      top: 18px;
      left: 18px;
      background: var(--accent);
      color: #fff;
      border: none;
      border-radius: 50%;
      width: 44px;
      height: 44px;
      font-size: 1.3em;
      cursor: pointer;
      z-index: 1000;
      box-shadow: 0 2px 8px #0002;
    }
    .alert {
      background: var(--danger);
      color: #fff;
      padding: 10px;
      border-radius: 5px;
      margin-bottom: 16px;
      text-align: center;
    }
    .center-link {
      text-align: center;
      margin-top: 10px;
    }
    .center-link a {
      color: var(--accent);
      text-decoration: none;
      font-size: 0.98em;
    }
    .center-link a:hover {
      text-decoration: underline;
    }
  </style>
</head>
<body>
  <button class="toggle-mode" onclick="toggleMode()" title="تبديل الوضع الليلي/النهاري">🌙</button>
  <div class="container" id="login-container">
    <h2>تسجيل الدخول</h2>
    <form id="login-form" autocomplete="off">
      <div class="form-group">
        <label for="username">اسم المستخدم</label>
        <input type="text" id="username" required>
      </div>
      <div class="form-group">
        <label for="password">كلمة المرور</label>
        <input type="password" id="password" required>
      </div>
      <div class="form-group">
        <label><input type="checkbox" id="use2fa"> تفعيل المصادقة الثنائية (اختياري)</label>
      </div>
      <button type="submit">دخول</button>
      <div class="center-link">
        <a href="#" onclick="showResetPassword();return false;">نسيت كلمة المرور؟</a>
      </div>
      <div id="login-error" class="alert" style="display:none;"></div>
    </form>
    <form id="reset-form" style="display:none;">
      <div class="form-group">
        <label for="reset-email">البريد الإلكتروني</label>
        <input type="text" id="reset-email" required>
      </div>
      <button type="submit">إعادة تعيين كلمة المرور</button>
      <div class="center-link">
        <a href="#" onclick="showLogin();return false;">عودة لتسجيل الدخول</a>
      </div>
      <div id="reset-msg" class="alert" style="display:none;"></div>
    </form>
  </div>
  <script>
    // مصفوفة لحفظ الأحداث مؤقتاً في الذاكرة
    var incidents = [];
    function toggleMode() {
      document.body.classList.toggle('dark-mode');
      document.querySelector('.toggle-mode').textContent = document.body.classList.contains('dark-mode') ? '☀️' : '🌙';
    }
    function showResetPassword() {
      document.getElementById('login-form').style.display = 'none';
      document.getElementById('reset-form').style.display = 'block';
    }
    function showLogin() {
      document.getElementById('reset-form').style.display = 'none';
      document.getElementById('login-form').style.display = 'block';
    }
    // تحقق من كلمة المرور: admin
    document.getElementById('login-form').onsubmit = function(e) {
      e.preventDefault();
      var username = document.getElementById('username').value.trim();
      var password = document.getElementById('password').value;
      var errorDiv = document.getElementById('login-error');
      errorDiv.style.display = 'none';
      if (!username || !password) {
        errorDiv.textContent = 'يرجى إدخال اسم المستخدم وكلمة المرور.';
        errorDiv.style.display = 'block';
        return;
      }
      if (password === 'admin') {
        document.getElementById('login-container').style.display = 'none';
        showDashboard();
      } else {
        errorDiv.textContent = 'كلمة المرور غير صحيحة.';
        errorDiv.style.display = 'block';
      }
    };
    document.getElementById('reset-form').onsubmit = function(e) {
      e.preventDefault();
      var msgDiv = document.getElementById('reset-msg');
      msgDiv.textContent = 'تم إرسال رابط إعادة تعيين كلمة المرور (نموذج تجريبي).';
      msgDiv.style.display = 'block';
    };
    // لوحة تحكم بسيطة بعد تسجيل الدخول
    function showDashboard() {
      removeDynamicContainers();
      var dash = document.createElement('div');
      dash.className = 'container';
      dash.id = 'dashboard';
      dash.innerHTML = `
        <h2>لوحة التحكم</h2>
        <div style="text-align:center; margin-bottom:20px;">
          <b>مرحباً بك في نظام تسجيل أحداث أمن المعلومات</b>
        </div>
        <ul style="list-style:none; padding:0;">
          <li><button onclick="showIncidentForm()">إدخال حدث أمني جديد</button></li>
          <li><button onclick="showIncidentLog()">سجل الأحداث</button></li>
          <li><button onclick="showStats()">الإحصائيات والرسم البياني</button></li>
          <li><button onclick="alert('سيتم إضافة إدارة المستخدمين لاحقاً')">إدارة المستخدمين</button></li>
        </ul>
        <div class="center-link" style="margin-top:24px;">
          <a href="#" onclick="logout();return false;">تسجيل الخروج</a>
        </div>
      `;
      document.body.appendChild(dash);
    }
    // سجل الأحداث
    function showIncidentLog() {
      removeDynamicContainers();
      var logDiv = document.createElement('div');
      logDiv.className = 'container';
      logDiv.id = 'incident-log-container';
      var tableRows = incidents.map(function(inc, i) {
        return `<tr>
          <td>${i+1}</td>
          <td>${inc.type}</td>
          <td>${inc.severity}</td>
          <td>${inc.datetime.replace('T',' ')}</td>
          <td>${inc.status}</td>
          <td><button onclick="showIncidentDetails(${i})">عرض</button></td>
        </tr>`;
      }).join('');
      logDiv.innerHTML = `
        <h2>سجل الأحداث</h2>
        <div style="overflow-x:auto;">
        <table style="width:100%; border-collapse:collapse; text-align:center;">
          <thead>
            <tr style="background:var(--primary); color:#fff;">
              <th>#</th>
              <th>النوع</th>
              <th>الخطورة</th>
              <th>التاريخ والوقت</th>
              <th>الحالة</th>
              <th>تفاصيل</th>
            </tr>
          </thead>
          <tbody>
            ${tableRows || '<tr><td colspan="6">لا توجد أحداث مسجلة</td></tr>'}
          </tbody>
        </table>
        </div>
        <div class="center-link" style="margin-top:18px;">
          <a href="#" onclick="showDashboard();return false;">عودة للوحة التحكم</a>
        </div>
      `;
      document.body.appendChild(logDiv);
    }

    // تفاصيل الحدث
    function showIncidentDetails(idx) {
      var inc = incidents[idx];
      if (!inc) return;
      removeDynamicContainers();
      var detDiv = document.createElement('div');
      detDiv.className = 'container';
      detDiv.id = 'incident-details-container';
      detDiv.innerHTML = `
        <h2>تفاصيل الحدث</h2>
        <ul style="list-style:none; padding:0;">
          <li><b>النوع:</b> ${inc.type}</li>
          <li><b>الخطورة:</b> ${inc.severity}</li>
          <li><b>التاريخ والوقت:</b> ${inc.datetime.replace('T',' ')}</li>
          <li><b>الوصف:</b> ${inc.desc}</li>
          <li><b>المصدر المشتبه به:</b> ${inc.source || '-'}</li>
          <li><b>الإجراءات المتخذة:</b> ${inc.actions}</li>
          <li><b>الحالة:</b> ${inc.status}</li>
        </ul>
        <div class="center-link" style="margin-top:18px;">
          <a href="#" onclick="showIncidentLog();return false;">عودة لسجل الأحداث</a>
        </div>
      `;
      document.body.appendChild(detDiv);
    }

    // شاشة الإحصائيات والرسم البياني
    function showStats() {
      removeDynamicContainers();
      var statsDiv = document.createElement('div');
      statsDiv.className = 'container';
      statsDiv.id = 'stats-container';
      // حساب عدد الأحداث حسب مستوى الخطورة
      var levels = ['منخفض','متوسط','عالي','حرج'];
      var colors = ['#1976d2','#ffa726','#d32f2f','#b71c1c'];
      var counts = levels.map(l => incidents.filter(inc => inc.severity === l).length);
      var total = counts.reduce((a,b)=>a+b,0);
      statsDiv.innerHTML = `
        <h2>إحصائيات الأحداث الأمنية</h2>
        <div style="text-align:center; margin-bottom:18px;">
          <b>عدد الأحداث الكلي: ${total}</b>
        </div>
        <canvas id="riskChart" width="320" height="220" style="display:block;margin:0 auto 18px auto;background:#f5f5f5;border-radius:8px;"></canvas>
        <div style="display:flex;justify-content:space-around;margin-bottom:10px;">
          ${levels.map((l,i)=>`<span style='color:${colors[i]};font-weight:bold;'>${l}: ${counts[i]}</span>`).join('')}
        </div>
        <div class="center-link" style="margin-top:18px;">
          <a href="#" onclick="showDashboard();return false;">عودة للوحة التحكم</a>
        </div>
      `;
      document.body.appendChild(statsDiv);
      // رسم الرسم البياني
      drawRiskChart(counts, levels, colors);
    }

    function drawRiskChart(counts, levels, colors) {
      var c = document.getElementById('riskChart');
      if (!c) return;
      var ctx = c.getContext('2d');
      ctx.clearRect(0,0,c.width,c.height);
      var max = Math.max(...counts,1);
      var barW = 50, gap = 30, baseY = 180;
      for(let i=0;i<counts.length;i++){
        var h = (counts[i]/max)*120;
        ctx.fillStyle = colors[i];
        ctx.fillRect(30+i*(barW+gap), baseY-h, barW, h);
        ctx.fillStyle = '#222';
        ctx.font = 'bold 16px Cairo,Arial';
        ctx.textAlign = 'center';
        ctx.fillText(counts[i], 30+i*(barW+gap)+barW/2, baseY-h-8);
      }
      // رسم المحاور
      ctx.strokeStyle = '#888';
      ctx.beginPath();
      ctx.moveTo(20,baseY); ctx.lineTo(c.width-10,baseY); ctx.stroke();
    }
    }

    // نموذج إدخال الحدث الأمني
    function showIncidentForm() {
      removeDynamicContainers();
      var formDiv = document.createElement('div');
      formDiv.className = 'container';
      formDiv.id = 'incident-form-container';
      formDiv.innerHTML = `
        <h2>إدخال حدث أمني جديد</h2>
        <form id="incident-form">
          <div class="form-group">
            <label for="type">نوع الحدث</label>
            <select id="type" required>
              <option value="">اختر النوع</option>
              <option>اختراق</option>
              <option>هجوم DDoS</option>
              <option>فيروس</option>
              <option>تسريب بيانات</option>
              <option>محاولة وصول غير مصرح</option>
              <option>أخرى</option>
            </select>
          </div>
          <div class="form-group">
            <label for="severity">مستوى الخطورة</label>
            <select id="severity" required>
              <option value="">اختر المستوى</option>
              <option>منخفض</option>
              <option>متوسط</option>
              <option>عالي</option>
              <option>حرج</option>
            </select>
          </div>
          <div class="form-group">
            <label for="datetime">التاريخ والوقت</label>
            <input type="datetime-local" id="datetime" required>
          </div>
          <div class="form-group">
            <label for="desc">الوصف التفصيلي للحدث</label>
            <textarea id="desc" rows="3" required></textarea>
          </div>
          <div class="form-group">
            <label for="source">المصدر المشتبه به (اختياري)</label>
            <input type="text" id="source">
          </div>
          <div class="form-group">
            <label for="actions">الإجراءات المتخذة</label>
            <textarea id="actions" rows="2" required></textarea>
          </div>
          <div class="form-group">
            <label for="attachments">المرفقات</label>
            <input type="file" id="attachments" multiple>
          </div>
          <div class="form-group">
            <label for="status">حالة الحدث</label>
            <select id="status" required>
              <option value="">اختر الحالة</option>
              <option>مفتوح</option>
              <option>قيد التحقيق</option>
              <option>مغلق</option>
            </select>
          </div>
          <button type="submit">حفظ الحدث</button>
          <div class="center-link" style="margin-top:10px;">
            <a href="#" onclick="showDashboard();return false;">عودة للوحة التحكم</a>
          </div>
          <div id="incident-msg" class="alert" style="display:none;"></div>
        </form>
      `;
      document.body.appendChild(formDiv);
      // تعبئة التاريخ والوقت الحالي افتراضياً
      var now = new Date();
      var local = now.toISOString().slice(0,16);
      document.getElementById('datetime').value = local;
      document.getElementById('incident-form').onsubmit = function(e) {
        e.preventDefault();
        document.getElementById('incident-msg').textContent = 'تم حفظ الحدث بنجاح (نموذج تجريبي).';
        document.getElementById('incident-msg').style.display = 'block';
        this.reset();
        // إعادة تعبئة التاريخ والوقت بعد الحفظ
        document.getElementById('datetime').value = new Date().toISOString().slice(0,16);
      };
    }

    // إزالة أي شاشة ديناميكية قبل عرض أخرى
    function removeDynamicContainers() {
      var ids = ['dashboard','incident-form-container','incident-log-container','incident-details-container','stats-container'];
      ids.forEach(function(id){
        var el = document.getElementById(id);
        if(el) el.remove();
      });
    }

    function logout() {
      removeDynamicContainers();
      document.getElementById('login-container').style.display = '';
      document.getElementById('login-form').reset();
      document.getElementById('login-error').style.display = 'none';
    }
  </script>
</body>
</html>
